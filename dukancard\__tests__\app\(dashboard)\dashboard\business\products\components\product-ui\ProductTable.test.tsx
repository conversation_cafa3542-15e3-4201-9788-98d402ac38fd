import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ProductTable from '@/app/(dashboard)/dashboard/business/products/components/product-ui/ProductTable';
import { useProducts } from '@/app/(dashboard)/dashboard/business/products/context/ProductsContext';
import { getProductWithVariants } from '@/app/(dashboard)/dashboard/business/products/actions/getProductWithVariants';
import { toast } from 'sonner';

// Mock dependencies
jest.mock('@/app/(dashboard)/dashboard/business/products/context/ProductsContext');
jest.mock('sonner');
jest.mock('@/app/(dashboard)/dashboard/business/products/actions/getProductWithVariants');
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props: any) => {
    // eslint-disable-next-line @next/next/no-img-element
    return <img {...props} alt={props.alt} />;
  },
}));
jest.mock('next/link', () => ({
    __esModule: true,
    default: ({ children, href }: { children: React.ReactNode, href: string }) => <a href={href}>{children}</a>,
}));
jest.mock('framer-motion', () => ({
    ...jest.requireActual('framer-motion'),
    motion: {
        ...jest.requireActual('framer-motion').motion,
        div: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
        tr: ({ children }: { children: React.ReactNode }) => <tr>{children}</tr>,
    },
    AnimatePresence: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));


const mockUseProducts = useProducts as jest.Mock;
const mockGetProductWithVariants = getProductWithVariants as jest.Mock;

const mockProducts = [
  {
    id: 'prod_1',
    name: 'Laptop Pro',
    description: 'A powerful laptop for professionals.',
    base_price: 120000,
    discounted_price: 110000,
    is_available: true,
    product_type: 'physical',
    image_url: '/laptop.jpg',
    variant_count: 2,
    available_variant_count: 2,
  },
  {
    id: 'prod_2',
    name: 'Web Design Service',
    description: 'Professional web design services.',
    base_price: 50000,
    is_available: true,
    product_type: 'service',
    image_url: null,
    variant_count: 0,
    available_variant_count: 0,
  },
];

const mockVariants = [
    { id: 'var_1', product_id: 'prod_1', variant_name: '16GB RAM', variant_values: { RAM: '16GB' }, base_price: 120000, discounted_price: null, is_available: true, images: [], featured_image_index: 0, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
    { id: 'var_2', product_id: 'prod_1', variant_name: '32GB RAM', variant_values: { RAM: '32GB' }, base_price: 140000, discounted_price: 135000, is_available: true, images: [], featured_image_index: 0, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
];


describe('ProductTable', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseProducts.mockReturnValue({
      products: [],
      isLoading: false,
      isPending: false,
      deletingProductId: null,
      setDeletingProductId: jest.fn(),
    });
  });

  it('should render an empty state when there are no products', () => {
    render(<ProductTable />);
    expect(screen.getByText("You haven't added any products or services yet")).toBeInTheDocument();
  });

  it('should render a table with product data', () => {
    mockUseProducts.mockReturnValue({
      ...mockUseProducts(),
      products: mockProducts,
    });
    render(<ProductTable />);
    expect(screen.getByText('Laptop Pro')).toBeInTheDocument();
    expect(screen.getByText('Web Design Service')).toBeInTheDocument();
    expect(screen.getByText('₹1,20,000.00')).toBeInTheDocument();
  });

  it('should call setDeletingProductId when delete button is clicked', async () => {
    const setDeletingProductId = jest.fn();
    mockUseProducts.mockReturnValue({
      ...mockUseProducts(),
      products: [mockProducts[0]],
      setDeletingProductId,
    });
    render(<ProductTable />);
    
    const user = userEvent.setup();
    const dropdownTrigger = screen.getByRole('button', { expanded: false });
    await user.click(dropdownTrigger);

    const deleteButton = screen.getByRole('menuitem', { name: /delete product/i });
    await user.click(deleteButton);

    expect(setDeletingProductId).toHaveBeenCalledWith('prod_1');
  });

  it('should expand a product to show variants when the expand button is clicked', async () => {
    mockUseProducts.mockReturnValue({
      ...mockUseProducts(),
      products: [mockProducts[0]],
    });
    mockGetProductWithVariants.mockResolvedValue({
      success: true,
      data: {
        ...mockProducts[0],
        variants: mockVariants,
        variant_count: mockVariants.length,
      }
    });
    
    render(<ProductTable />);
    
    const user = userEvent.setup();
    const expandButton = screen.getAllByRole('button')[0]; // First button is the expand button
    await user.click(expandButton);

    expect(mockGetProductWithVariants).toHaveBeenCalledWith('prod_1');
    expect(await screen.findAllByText('Product Variants')).toHaveLength(2);
    expect(await screen.findByText('16GB RAM')).toBeInTheDocument();
  });

  it('should show an error toast if fetching variants fails', async () => {
    mockUseProducts.mockReturnValue({
      ...mockUseProducts(),
      products: [mockProducts[0]],
    });
    mockGetProductWithVariants.mockResolvedValue({ success: false, error: 'Failed to fetch' });
    
    render(<ProductTable />);
    
    const user = userEvent.setup();
    const expandButton = screen.getAllByRole('button')[0]; // First button is the expand button
    await user.click(expandButton);

    expect(toast.error).toHaveBeenCalledWith('Failed to load product variants');
  });

  it('should apply a pending style when a product is being deleted', () => {
    mockUseProducts.mockReturnValue({
      ...mockUseProducts(),
      products: mockProducts,
      isPending: true,
      deletingProductId: 'prod_1',
    });
    render(<ProductTable />);
    const productRow = screen.getByText('Laptop Pro').closest('tr');
    expect(productRow).toHaveClass('opacity-50');
    expect(productRow).toHaveClass('pointer-events-none');
  });
});