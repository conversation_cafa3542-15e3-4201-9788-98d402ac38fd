import { render, screen, fireEvent, waitFor, act } from "@testing-library/react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import ProductsPageClient from "@/app/(dashboard)/dashboard/business/products/components/ProductsPageClient";
import * as productActions from "@/app/(dashboard)/dashboard/business/products/actions/getProducts";

// Mock dependencies
jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
}));

jest.mock("sonner", () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

jest.mock("@/app/(dashboard)/dashboard/business/products/actions/getProducts", () => ({
  getProductServices: jest.fn(),
}));

const mockRouter = {
  push: jest.fn(),
};

const mockGetProductServices = productActions.getProductServices as jest.Mock;

const mockProduct = (id: string, name: string) => ({
    id,
    name,
    base_price: 100,
    is_available: true,
    variant_count: 0,
    has_variants: false,
    available_variant_count: 0,
    business_id: "biz-123",
    product_type: "physical" as "physical" | "service",
    description: `Description for ${name}`,
    discounted_price: null,
    image_url: null,
    images: [],
    featured_image_index: 0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    slug: name.toLowerCase().replace(/ /g, "-"),
});


describe("Business Dashboard - Product Page Integration Tests", () => {
  beforeAll(() => {
    jest.useFakeTimers();
  });

  beforeEach(() => {
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    mockGetProductServices.mockResolvedValue({ data: [], count: 0 });
    jest.clearAllMocks();
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  it("should display the product page with a list of products", async () => {
    const mockProducts = [mockProduct("1", "Product 1"), mockProduct("2", "Product 2")];
    mockGetProductServices.mockResolvedValue({ data: mockProducts, count: 2 });

    render(<ProductsPageClient initialData={mockProducts} initialCount={2} planLimit={10} />);

    await waitFor(() => {
        expect(screen.getByText("Product 1")).toBeInTheDocument();
        expect(screen.getByText("Product 2")).toBeInTheDocument();
    });
  });

  it("should filter products when user types in search bar", async () => {
    const initialProducts = [mockProduct("1", "Apple iPhone"), mockProduct("2", "Samsung Galaxy")];
    const filteredProducts = [mockProduct("1", "Apple iPhone")];
    
    mockGetProductServices.mockResolvedValueOnce({ data: initialProducts, count: 2 });

    render(<ProductsPageClient initialData={initialProducts} initialCount={2} planLimit={10} />);

    await waitFor(() => {
        expect(screen.getByText("Apple iPhone")).toBeInTheDocument();
    });

    mockGetProductServices.mockResolvedValue({ data: filteredProducts, count: 1 });

    const searchInput = screen.getByPlaceholderText(/search by name, description, or sku.../i);
    fireEvent.change(searchInput, { target: { value: "Apple" } });

    await act(async () => {
        jest.advanceTimersByTime(500); // Advance timers to trigger debounce
    });

    await waitFor(() => {
        expect(mockGetProductServices).toHaveBeenCalledWith(1, 10, { searchTerm: "Apple" }, "created_desc");
        expect(screen.queryByText("Samsung Galaxy")).not.toBeInTheDocument();
    });
  });

  it("should handle pagination correctly", async () => {
    const mockProductsPage1 = Array.from({ length: 10 }, (_, i) => mockProduct(`${i + 1}`, `Product ${i + 1}`));
    mockGetProductServices.mockResolvedValueOnce({ data: mockProductsPage1, count: 15 });

    render(<ProductsPageClient initialData={mockProductsPage1} initialCount={15} planLimit={20} />);

    await waitFor(() => {
        expect(screen.getByText("Product 10")).toBeInTheDocument();
    });

    const mockProductsPage2 = Array.from({ length: 5 }, (_, i) => mockProduct(`${i + 11}`, `Product ${i + 11}`));
    mockGetProductServices.mockResolvedValueOnce({ data: mockProductsPage2, count: 15 });

    const nextButton = screen.getByRole("button", { name: /next/i });
    await act(async () => {
        fireEvent.click(nextButton);
    });

    await waitFor(() => {
        expect(mockGetProductServices).toHaveBeenCalledWith(2, 10, { searchTerm: "" }, "created_desc");
    });
  });
});